"""
API server for v-switch using FastAPI.
"""

import logging
import threading
import asyncio
from typing import Dict, Any, Optional
import uvicorn
from fastapi import FastAPI, HTTPException, Query, Path as PathParam
from pydantic import BaseModel
from ..config.server_config import ServerConfig
from ..core_service.core_service import CoreService

class TestRequest(BaseModel):
    """Request model for test endpoint."""
    tenant_id: str

class TestDeleteRequest(BaseModel):
    """Request model for deleting test resources."""
    tenant_id: str

# Pydantic models for request/response data
class SubnetGatewayRequest(BaseModel):
    """Request model for creating subnet gateway."""
    tenant_id: str
    vlan_id: int
    subnet_gw_ip: str

class SubnetGatewayDeleteRequest(BaseModel):
    """Request model for deleting subnet gateway."""
    tenant_id: str
    vlan_id: int

class EIPRequest(BaseModel):
    """Request model for creating EIP."""
    tenant_id: str
    vlan_id: int
    eip: str
    gateway_ip: str
    internal_ip: str

class EIPDeleteRequest(BaseModel):
    """Request model for deleting EIP."""
    tenant_id: str
    vlan_id: int
    eip: str
    internal_ip: str


class SuccessResponse(BaseModel):
    """Success response model."""
    success: bool = True
    request_id: Optional[str] = None
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None


class ErrorResponse(BaseModel):
    """Error response model."""
    error: bool = True
    message: str
    status_code: int


class HealthResponse(BaseModel):
    """Health check response model."""
    success: bool = True
    status: str = "healthy"
    running: bool


class StatusResponse(BaseModel):
    """Server status response model."""
    success: bool = True
    running: bool
    metadata: Optional[Dict[str, Any]] = None


class InstructionResponse(BaseModel):
    """Instruction status response model."""
    success: bool = True
    instruction: Optional[Dict[str, Any]] = None


def create_fastapi_app(core_service: CoreService) -> FastAPI:
    """Create FastAPI application with all routes.

    Args:
        core_service: Core service instance

    Returns:
        FastAPI application instance
    """
    app = FastAPI(
        title="V-Switch API",
        description="API server for v-switch network management",
        version="1.0.0"
    )

    logger = logging.getLogger(__name__)

    # Health check endpoint
    @app.get("/health", response_model=HealthResponse)
    async def health_check():
        """Health check endpoint."""
        try:
            return HealthResponse(
                success=True,
                status="healthy",
                running=core_service.is_running()
            )
        except Exception as e:
            logger.error(f"Error in health check: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # Server status endpoint
    @app.get("/status", response_model=StatusResponse)
    async def server_status():
        """Get server status."""
        try:
            metadata = core_service.get_server_metadata()
            return StatusResponse(
                success=True,
                running=core_service.is_running(),
                metadata=metadata
            )
        except Exception as e:
            logger.error(f"Error getting server status: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # Create test endpoint
    @app.post("/network/test", response_model=SuccessResponse)
    async def test_endpoint(request: TestRequest):
        """Test endpoint."""
        try:
            logger.info(f"Creating test for tenant {request.tenant_id}")

            request_id = core_service.create_test(request.tenant_id)

            if request_id:
                return SuccessResponse(
                    success=True,
                    request_id=request_id,
                    message="Test creation request submitted"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to create test")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating test: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # Delete test endpoint
    @app.delete("/network/test", response_model=SuccessResponse)
    async def delete_test_endpoint(request: TestDeleteRequest):
        """Delete test resources."""
        try:
            logger.info(f"Deleting test for tenant {request.tenant_id}")

            request_id = core_service.delete_test(request.tenant_id)

            if request_id:
                return SuccessResponse(
                    success=True,
                    request_id=request_id,
                    message="Test deletion request submitted"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to delete test")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting test: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # Create subnet gateway endpoint
    @app.post("/network/subnet-gateway", response_model=SuccessResponse)
    async def create_subnet_gateway(request: SubnetGatewayRequest):
        """Create subnet gateway."""
        try:
            logger.info(f"Creating subnet gateway for tenant {request.tenant_id}")

            request_id = core_service.create_subnet_gateway(
                request.tenant_id,
                request.vlan_id,
                request.subnet_gw_ip
            )

            if request_id:
                return SuccessResponse(
                    success=True,
                    request_id=request_id,
                    message="Subnet gateway creation request submitted"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to create subnet gateway")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating subnet gateway: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # Delete subnet gateway endpoint
    @app.delete("/network/subnet-gateway", response_model=SuccessResponse)
    async def delete_subnet_gateway(request: SubnetGatewayDeleteRequest):
        """Delete subnet gateway."""
        try:
            logger.info(f"Deleting subnet gateway for tenant {request.tenant_id}")

            request_id = core_service.delete_subnet_gateway(
                request.tenant_id,
                str(request.vlan_id)
            )

            if request_id:
                return SuccessResponse(
                    success=True,
                    request_id=request_id,
                    message="Subnet gateway deletion request submitted"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to delete subnet gateway")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting subnet gateway: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # Create EIP endpoint
    @app.post("/network/eip", response_model=SuccessResponse)
    async def create_eip(request: EIPRequest):
        """Create EIP."""
        try:
            logger.info(f"Creating EIP for tenant {request.tenant_id}")

            request_id = core_service.create_eip(
                request.tenant_id,
                request.vlan_id,
                request.eip,
                request.gateway_ip,
                request.internal_ip
            )

            if request_id:
                return SuccessResponse(
                    success=True,
                    request_id=request_id,
                    message="EIP creation request submitted"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to create EIP")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating EIP: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # Delete EIP endpoint
    @app.delete("/network/eip", response_model=SuccessResponse)
    async def delete_eip(request: EIPDeleteRequest):
        """Delete EIP."""
        try:
            logger.info(f"Deleting EIP for tenant {request.tenant_id}")

            request_id = core_service.delete_eip(
                request.tenant_id,
                str(request.vlan_id),
                request.eip,
                request.internal_ip
            )

            if request_id:
                return SuccessResponse(
                    success=True,
                    request_id=request_id,
                    message="EIP deletion request submitted"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to delete EIP")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting EIP: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # Get instruction status endpoint
    @app.get("/instruction/{request_id}", response_model=InstructionResponse)
    async def get_instruction_status(
        request_id: str = PathParam(..., description="Request identifier"),
        tenant_id: str = Query(..., description="Tenant identifier")
    ):
        """Get instruction status."""
        try:
            logger.info(f"Getting instruction status for request {request_id}")

            instruction_data = core_service.get_instruction_status(request_id, tenant_id)

            if instruction_data:
                return InstructionResponse(
                    success=True,
                    instruction=instruction_data
                )
            else:
                raise HTTPException(status_code=404, detail="Instruction not found")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting instruction status: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # Delete instruction endpoint
    @app.delete("/instruction/{request_id}", response_model=SuccessResponse)
    async def delete_instruction(
        request_id: str = PathParam(..., description="Request identifier"),
        tenant_id: str = Query(..., description="Tenant identifier")
    ):
        """Delete instruction."""
        try:
            logger.info(f"Deleting instruction for request {request_id}")

            success = core_service.delete_instruction(request_id, tenant_id)

            if success:
                return SuccessResponse(
                    success=True,
                    message="Instruction deleted successfully"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to delete instruction")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting instruction: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    return app


class APIServer:
    """Main API server class using FastAPI."""

    def __init__(self, config: ServerConfig, core_service: CoreService):
        """Initialize API server.

        Args:
            config: Server configuration
            core_service: Core service instance
        """
        self.config = config
        self.core_service = core_service
        self.logger = logging.getLogger(__name__)

        # Create FastAPI app
        self.app = create_fastapi_app(core_service)

        # Server state
        self.server = None
        self.server_thread = None
        self._running = False
    
    def start(self) -> bool:
        """Start the API server.

        Returns:
            True if successful
        """
        try:
            if self._running:
                self.logger.warning("API server is already running")
                return True

            # Configure uvicorn
            config = uvicorn.Config(
                app=self.app,
                host="0.0.0.0",
                port=self.config.server.port,
                log_level="info",
                access_log=True
            )

            self.server = uvicorn.Server(config)

            # Start server in background thread
            self.server_thread = threading.Thread(
                target=self._run_server,
                name="APIServer",
                daemon=True
            )

            self._running = True
            self.server_thread.start()

            self.logger.info(f"FastAPI server started on port {self.config.server.port}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start API server: {e}")
            return False
    
    def stop(self) -> None:
        """Stop the API server."""
        try:
            if not self._running:
                return

            self.logger.info("Stopping API server...")
            self._running = False

            # Shutdown uvicorn server
            if self.server:
                self.server.should_exit = True

            # Wait for server thread to finish
            if self.server_thread and self.server_thread.is_alive():
                self.server_thread.join(timeout=5.0)

            self.logger.info("API server stopped")

        except Exception as e:
            self.logger.error(f"Error stopping API server: {e}")
    
    def _run_server(self) -> None:
        """Run the uvicorn server."""
        try:
            self.logger.info("Starting FastAPI server loop")
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.server.serve())
        except Exception as e:
            if self._running:  # Only log if we're supposed to be running
                self.logger.error(f"FastAPI server error: {e}")
    
    def is_running(self) -> bool:
        """Check if API server is running.
        
        Returns:
            True if running
        """
        return self._running
