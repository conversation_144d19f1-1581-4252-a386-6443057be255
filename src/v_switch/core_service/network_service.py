"""
Network service for creating subnet gateways and EIPs.
"""

import uuid
import logging
from typing import Dict, Any, List, Optional
from ..common.etcd_client import ETCDClient
from ..config.server_config import ServerConfig
from .shard_manager import ShardManager


class NetworkService:
    """处理网络配置请求。"""
    
    def __init__(self, etcd_client: ETCDClient, config: ServerConfig, shard_manager: ShardManager):
        """初始化网络服务。
        
        Args:
            etcd_client: ETCD client instance
            config: Server configuration
            shard_manager: Shard manager instance
        """
        self.etcd_client = etcd_client
        self.config = config
        self.shard_manager = shard_manager
        self.logger = logging.getLogger(__name__)
    
    def create_test_instruction(self, tenant_id: str) -> Optional[str]:
        """测试创建子网网关。
        
        Args:
            tenant_id: Tenant identifier
            
        Returns:
            Request ID if successful, None otherwise
        """
        try:
            # Generate unique request ID
            request_id = str(uuid.uuid4())

            # Get shard for tenant
            shard_id = self.shard_manager.get_shard_for_tenant(tenant_id)

            # Create instruction
            instruction = {
                "type": "test",
                "tenant_id": tenant_id,
                "shard_id": shard_id,
                "request_id": request_id,
                "cmds": [
                    "echo 'Test command executed 1'",
                    "echo 'Test command executed 2'",
                    "echo 'Test command executed 3'"
                    ],
                "revocation": [                    
                    "echo 'Test revocation executed 1'",
                    "echo 'Test revocation executed 2'",
                    "echo 'Test revocation executed 3'"
                    ]
            }
            
            
            # Put instruction to shard
            success = self.shard_manager.put_instruction(shard_id, request_id, instruction)
            
            if success:
                self.logger.info(f"Created test instruction {request_id} for tenant {tenant_id}")
                return request_id
            else:
                self.logger.error(f"Failed to create test instruction for tenant {tenant_id}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error creating test for tenant {tenant_id}: {e}")
            return None
        
    def create_subnet_gateway(self, tenant_id: str, vlan_id: str, subnet_gw_ip: str) -> Optional[str]:
        """创建子网网关。
        
        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            subnet_gw_ip: Subnet gateway IP
            
        Returns:
            Request ID if successful, None otherwise
        """
        try:
            # Generate unique request ID
            request_id = str(uuid.uuid4())
                        
            # Get shard for tenant
            shard_id = self.shard_manager.get_shard_for_tenant(tenant_id)

            # Generate commands from template
            cmds = self._generate_subnet_gateway_commands(vlan_id, subnet_gw_ip)
            revocation = self._generate_subnet_gateway_revocation(vlan_id)
            
            # Create instruction
            instruction = {
                "type": "create_gateway",
                "tenant_id": tenant_id,
                "shard_id": shard_id,
                "request_id": request_id,
                "cmds": cmds,
                "revocation": revocation
            }
            
            # Put instruction to shard
            success = self.shard_manager.put_instruction(shard_id, request_id, instruction)
            
            if success:
                self.logger.info(f"Created subnet gateway instruction {request_id} for tenant {tenant_id}")
                return request_id
            else:
                self.logger.error(f"Failed to create subnet gateway instruction for tenant {tenant_id}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error creating subnet gateway for tenant {tenant_id}: {e}")
            return None
    
    def create_eip(self, tenant_id: str, vlan_id: str, eip: str, 
                   gateway_ip: str, internal_ip: str) -> Optional[str]:
        """创建 EIP。
        
        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            eip: External IP address
            gateway_ip: Gateway IP address
            internal_ip: Internal IP address
            
        Returns:
            Request ID if successful, None otherwise
        """
        try:
            # Generate unique request ID
            request_id = str(uuid.uuid4())
                        
            # Get shard for tenant
            shard_id = self.shard_manager.get_shard_for_tenant(tenant_id)
            
            # Generate commands from template
            cmds = self._generate_eip_commands(vlan_id, eip, gateway_ip, internal_ip)
            revocation = self._generate_eip_revocation(vlan_id, eip, internal_ip)
            
            # Create instruction
            instruction = {
                "type": "create_eip",
                "tenant_id": tenant_id,
                "shard_id": shard_id,
                "request_id": request_id,
                "cmds": cmds,
                "revocation": revocation
            }

            # Put instruction to shard
            success = self.shard_manager.put_instruction(shard_id, request_id, instruction)
            
            if success:
                self.logger.info(f"Created EIP instruction {request_id} for tenant {tenant_id}")
                return request_id
            else:
                self.logger.error(f"Failed to create EIP instruction for tenant {tenant_id}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error creating EIP for tenant {tenant_id}: {e}")
            return None
    
    def _generate_subnet_gateway_commands(self, vlan_id: str, subnet_gw_ip: str) -> List[str]:
        """为创建子网网关生成命令。
        
        Args:
            vlan_id: VLAN identifier
            subnet_gw_ip: Subnet gateway IP
            
        Returns:
            List of commands
        """
        return [
            f"ip netns add ns-vlan{vlan_id}",
            f"ip link add v-lan-host-{vlan_id} type veth peer name v-lan-ns-{vlan_id}",
            f"ip link set v-lan-ns-{vlan_id} netns ns-vlan{vlan_id}",
            f"ip netns exec ns-vlan{vlan_id} ip addr add {subnet_gw_ip}/24 dev v-lan-ns-{vlan_id}",
            f"ip netns exec ns-vlan{vlan_id} ip addr",
            f"ovs-vsctl add-port br-vlan v-lan-host-{vlan_id} tag={vlan_id}",
            f"ip link set v-lan-host-{vlan_id} up",
            f"ip netns exec ns-vlan{vlan_id} ip link set v-lan-ns-{vlan_id} up",
            f"ip netns exec ns-vlan{vlan_id} ip link set lo up",
            f"ip link set v-lan-host-{vlan_id} up"
        ]
    
    def _generate_subnet_gateway_revocation(self, vlan_id: str) -> List[str]:
        """为子网网关生成撤销命令。

        Args:
            vlan_id: VLAN identifier

        Returns:
            List of revocation commands
        """
        return [
            f"ovs-vsctl del-port br-vlan v-lan-host-{vlan_id}",
            f"ip link delete v-lan-host-{vlan_id}",
            f"ip netns delete ns-vlan{vlan_id}"
        ]

    def _generate_eip_commands(self, vlan_id: str, eip: str, gateway_ip: str, internal_ip: str) -> List[str]:
        """为创建 EIP 生成命令。

        Args:
            vlan_id: VLAN identifier
            eip: External IP address
            gateway_ip: Gateway IP address
            internal_ip: Internal IP address

        Returns:
            List of commands
        """
        return [
            f"ip link add v-eip-host-{vlan_id} type veth peer name v-eip-ns-{vlan_id}",
            f"ip link set v-eip-ns-{vlan_id} netns ns-vlan{vlan_id}",
            f"ip netns exec ns-vlan{vlan_id} ip addr add {eip}/24 dev v-eip-ns-{vlan_id}",
            f"ovs-vsctl add-port br-eip v-eip-host-{vlan_id}",
            f"ip link set v-eip-host-{vlan_id} up",
            f"ip netns exec ns-vlan{vlan_id} ip link set v-eip-ns-{vlan_id} up",
            f"ip netns exec ns-vlan{vlan_id} ip link set lo up",
            f"ip netns exec ns-vlan{vlan_id} ip route add default via {gateway_ip} dev v-eip-ns-{vlan_id}",
            f"ip netns exec ns-vlan{vlan_id} ip addr",
            f"ip netns exec ns-vlan{vlan_id} iptables -t nat -L -n -v --line-numbers",
            f"ip netns exec ns-vlan{vlan_id} iptables -t nat -A PREROUTING -d {eip} -j DNAT --to-destination {internal_ip}",
            f"ip netns exec ns-vlan{vlan_id} iptables -t nat -A POSTROUTING -s {internal_ip} -j SNAT --to-source {eip}",
            f"ip netns exec ns-vlan{vlan_id} iptables -t nat -D POSTROUTING 1",
            f"ip netns exec ns-vlan{vlan_id} nft add rule ip nat POSTROUTING ip saddr {gateway_ip} ip daddr != {{ 10.0.0.0/8, **********/12, ***********/24,***********/24 }} snat to {eip}",
            f"ip netns exec ns-vlan{vlan_id} nft list table ip nat"
        ]

    def _generate_eip_revocation(self, vlan_id: str, eip: str, internal_ip: str) -> List[str]:
        """为 EIP 生成撤销命令。

        Args:
            vlan_id: VLAN identifier
            eip: External IP address
            internal_ip: Internal IP address

        Returns:
            List of revocation commands
        """
        return [
            f"ip netns exec ns-vlan{vlan_id} iptables -t nat -D PREROUTING -d {eip} -j DNAT --to-destination {internal_ip}",
            f"ip netns exec ns-vlan{vlan_id} iptables -t nat -D POSTROUTING -s {internal_ip} -j SNAT --to-source {eip}",
            f"ip netns exec ns-vlan{vlan_id} nft delete rule ip nat POSTROUTING handle $(ip netns exec ns-vlan{vlan_id} nft -a list table ip nat | grep 'snat to {eip}' | awk '{{print $NF}}')",
            f"ovs-vsctl del-port br-eip v-eip-host-{vlan_id}",
            f"ip link delete v-eip-host-{vlan_id}"
        ]

    def get_instruction_status(self, request_id: str, tenant_id: str) -> Optional[Dict[str, Any]]:
        """获取指令的状态。

        Args:
            request_id: Request identifier
            tenant_id: Tenant identifier

        Returns:
            Instruction data or None if not found
        """
        try:
            shard_id = self.shard_manager.get_shard_for_tenant(tenant_id)
            return self.shard_manager.get_instruction(shard_id, request_id)

        except Exception as e:
            self.logger.error(f"Error getting instruction status {request_id}: {e}")
            return None

    def delete_instruction(self, request_id: str, tenant_id: str) -> bool:
        """删除指令。

        Args:
            request_id: Request identifier
            tenant_id: Tenant identifier

        Returns:
            True if successful
        """
        try:
            shard_id = self.shard_manager.get_shard_for_tenant(tenant_id)
            return self.shard_manager.delete_instruction(shard_id, request_id)

        except Exception as e:
            self.logger.error(f"Error deleting instruction {request_id}: {e}")
            return False
