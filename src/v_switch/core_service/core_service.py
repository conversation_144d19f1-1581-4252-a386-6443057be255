"""
Core service for v-switch.
"""

import logging
import threading
from typing import Optional
from ..common.etcd_client import ETCDClient
from ..config.server_config import ServerConfig
from .shard_manager import ShardManager
from .agent_manager import AgentManager
from .network_service import NetworkService


class CoreService:
    """主要核心服务类别协调所有组件。"""
    
    def __init__(self, config: ServerConfig):
        """初始化核心服务。
        
        Args:
            config: Server configuration
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 初始化 ETCD 客户端
        self.etcd_client = ETCDClient(
            host=config.etcd.host,
            port=config.etcd.port,
            timeout=config.etcd.timeout,
            username=config.etcd.username,
            password=config.etcd.password
        )
        
        # 初始化管理器
        self.shard_manager = ShardManager(self.etcd_client, config)
        self.agent_manager = AgentManager(self.etcd_client, config)
        self.network_service = NetworkService(self.etcd_client, config, self.shard_manager)
        
        # 多线程
        self._agent_monitor_thread = None
        self._running = False
    
    def initialize(self) -> bool:
        """初始化核心服务。
        
        Returns:
            True if successful
        """
        try:
            self.logger.info("Initializing core service...")
            
            # 连接到 ETCD
            self.etcd_client.connect()

            # 初始化分片
            if not self.shard_manager.initialize_shards():
                # self.logger.error("Failed to initialize shards")
                return False
            
            # 先检查一次agent状态
            if not self.agent_manager.check_agent_status():
                self.logger.error("Metadata check failed")
                return False
            # 初始化元数据
            if not self.agent_manager.initialize_metadata():
                self.logger.error("Failed to initialize metadata")
                return False
            
            self.logger.info("Core service initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize core service: {e}")
            return False
    
    def start(self) -> bool:
        """启动核心服务。
        
        Returns:
            True if successful
        """
        try:
            if not self.initialize():
                return False
            
            self._running = True
            
            # 在后台线程中启动 agent 监控
            self._agent_monitor_thread = threading.Thread(
                target=self._run_agent_monitoring,
                name="AgentMonitor",
                daemon=True
            )
            self._agent_monitor_thread.start()
            
            self.logger.info("Core service started successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start core service: {e}")
            return False
    
    def stop(self) -> None:
        """停止核心服务。"""
        try:
            self.logger.info("Stopping core service...")
            self._running = False
            
            # 等待 agent 监控线程完成
            if self._agent_monitor_thread and self._agent_monitor_thread.is_alive():
                self._agent_monitor_thread.join(timeout=5.0)
            
            self.logger.info("Core service stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping core service: {e}")
    
    def _run_agent_monitoring(self) -> None:
        """在后台线程中运行 agent 监控。"""
        try:
            self.logger.info("Starting agent monitoring thread")
            self.agent_manager.start_agent_monitoring()
        except Exception as e:
            self.logger.error(f"Agent monitoring thread error: {e}")
    
    def create_test(self, tenant_id: str) -> Optional[str]:
        """创建测试指令。
        
        Args:
            tenant_id: Tenant identifier
            
        Returns:
            Request ID if successful, None otherwise
        """
        # 这里可以添加更多的逻辑来处理测试指令
        return self.network_service.create_test_instruction(tenant_id)

    def create_subnet_gateway(self, tenant_id: str, vlan_id: str, subnet_gw_ip: str) -> Optional[str]:
        """创建子网网关。
        
        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            subnet_gw_ip: Subnet gateway IP
            
        Returns:
            Request ID if successful, None otherwise
        """
        return self.network_service.create_subnet_gateway(tenant_id, vlan_id, subnet_gw_ip)
    
    def create_eip(self, tenant_id: str, vlan_id: str, eip: str, 
                   gateway_ip: str, internal_ip: str) -> Optional[str]:
        """创建 EIP。
        
        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            eip: External IP address
            gateway_ip: Gateway IP address
            internal_ip: Internal IP address
            
        Returns:
            Request ID if successful, None otherwise
        """
        return self.network_service.create_eip(tenant_id, vlan_id, eip, gateway_ip, internal_ip)
    
    def get_instruction_status(self, request_id: str, tenant_id: str) -> Optional[dict]:
        """获取指令的状态。
        
        Args:
            request_id: Request identifier
            tenant_id: Tenant identifier
            
        Returns:
            Instruction data or None if not found
        """
        return self.network_service.get_instruction_status(request_id, tenant_id)
    
    def delete_instruction(self, request_id: str, tenant_id: str) -> bool:
        """删除指令。
        
        Args:
            request_id: Request identifier
            tenant_id: Tenant identifier
            
        Returns:
            True if successful
        """
        return self.network_service.delete_instruction(request_id, tenant_id)
    
    def get_server_metadata(self) -> Optional[dict]:
        """获取服务器元数据。
        
        Returns:
            Server metadata or None if not found
        """
        return self.agent_manager.get_metadata()
    
    def is_running(self) -> bool:
        """检查核心服务是否正在运行。
        
        Returns:
            True if running
        """
        return self._running
