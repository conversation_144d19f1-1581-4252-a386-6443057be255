"""
Server configuration for v-switch core service.
"""

from typing import Dict, Any, Optional
from dataclasses import dataclass
from .base_config import BaseConfig


@dataclass
class ServerSettings:
    """服务器特定的设置。"""
    port: int = 30090
    shard_count: int = 32


class ServerConfig(BaseConfig):
    """v-switch 核心服务的配置。"""
    
    def __init__(self, config_file: Optional[str] = None):
        """初始化服务配置。
        
        Args:
            config_file: Path to YAML configuration file
        """
        super().__init__(config_file)
        self.server = ServerSettings()
        
        if config_file:
            self.load_from_file(config_file)
    
    def _update_from_dict(self, config_data: Dict[str, Any]) -> None:
        """从字典中更新配置。
        
        Args:
            config_data: Configuration dictionary
        """
        # 调用常见配置的父方法
        super()._update_from_dict(config_data)
        
        # 更新服务器特定的配置
        if 'server' in config_data:
            server_config = config_data['server']
            for key, value in server_config.items():
                if hasattr(self.server, key):
                    setattr(self.server, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典。
        
        Returns:
            Configuration as dictionary
        """
        config_dict = super().to_dict()
        config_dict['server'] = {
            'port': self.server.port,
            'shard_count': self.server.shard_count,
        }
        return config_dict
    
    def get_shard_count(self) -> int:
        """获取分片数量。
        
        Returns:
            Number of shards
        """
        return self.server.shard_count
    
    def get_shard_paths(self) -> list[str]:
        """获取ETCD的分片路径列表。
        
        Returns:
            List of shard paths like ['/network/server/instruct/1', ...]
        """
        return [f"/network/server/instruct/{i+1}" 
            for i in range(self.server.shard_count)]
    
    # def get_shard_for_tenant(self, tenant_id: str) -> str:
    #     """通过Hash计算获得租户的分片值。
        
    #     Args:
    #         tenant_id: Tenant identifier
            
    #     Returns:
    #         Shard path for the tenant
    #     """
    #     # 使用一致性哈希算法计算分片值
    #     shard_index = hash(tenant_id) % self.server.shard_count
    #     return f"{shard_index + 1}"
