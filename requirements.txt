# Production dependencies for v-switch agent

# Optional dependencies for enhanced functionality
# Install with: pip install -r requirements.txt

# Core dependencies (always required)
# None - the agent works with Python standard library only

# Optional dependencies for enhanced features:
# traitlets>=5.0.0    # Enhanced configuration with type checking
# etcd3>=0.12.0       # ETCD client for distributed coordination
# PyYAML>=6.0         # YAML configuration file support

# To install all optional dependencies:
# pip install traitlets etcd3 PyYAML
protobuf==3.20.3
uhashring==2.4
pyyaml==6.0.2
etcd3==0.12.0
grpcio==1.73.1
six==1.17.0 
tenacity==9.1.2
click==8.2.1 
h11==0.16.0 
typing-extensions==4.14.1 
uvicorn[standard]>=0.24.0
fastapi==0.104.0
pydantic==2.11.7