#!/usr/bin/env python3
"""
Test script for FastAPI implementation of v-switch API server.
"""

import sys
import os
import time
import requests
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from v_switch.config.server_config import ServerConfig
from v_switch.core_service.core_service import CoreService
from v_switch.api_server.api_server import APIServer


def test_api_endpoints():
    """Test API endpoints."""
    base_url = "http://localhost:30090"
    
    print("Testing API endpoints...")
    
    # Test health check
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"✓ Health check: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"✗ Health check failed: {e}")
        return False
    
    # Test server status
    try:
        response = requests.get(f"{base_url}/status", timeout=5)
        print(f"✓ Server status: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"✗ Server status failed: {e}")
        return False
    
    # Test create subnet gateway
    try:
        data = {
            "tenant_id": "test-tenant-1",
            "vlan_id": 100,
            "subnet_gw_ip": "***********"
        }
        response = requests.post(f"{base_url}/network/subnet-gateway", 
                               json=data, timeout=5)
        print(f"✓ Create subnet gateway: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"✗ Create subnet gateway failed: {e}")
    
    # Test create EIP
    try:
        data = {
            "tenant_id": "test-tenant-1",
            "vlan_id": 100,
            "eip": "***********",
            "gateway_ip": "***********",
            "internal_ip": "***********0"
        }
        response = requests.post(f"{base_url}/network/eip", 
                               json=data, timeout=5)
        print(f"✓ Create EIP: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"✗ Create EIP failed: {e}")
    
    return True


def main():
    """Main function."""
    print("Starting FastAPI v-switch server test...")
    
    try:
        # Load configuration
        config_file = "config/server_config.yaml"
        if not os.path.exists(config_file):
            print(f"✗ Configuration file not found: {config_file}")
            return 1
        
        config = ServerConfig.from_file(config_file)
        print(f"✓ Configuration loaded from {config_file}")
        
        # Create core service
        core_service = CoreService(config)
        print("✓ Core service created")
        
        # Create API server
        api_server = APIServer(config, core_service)
        print("✓ API server created")
        
        # Start core service
        if not core_service.start():
            print("✗ Failed to start core service")
            return 1
        print("✓ Core service started")
        
        # Start API server
        if not api_server.start():
            print("✗ Failed to start API server")
            core_service.stop()
            return 1
        print("✓ API server started")
        
        # Wait a moment for server to be ready
        time.sleep(2)
        
        # Test API endpoints
        if test_api_endpoints():
            print("✓ All API tests passed")
        else:
            print("✗ Some API tests failed")
        
        # Stop services
        print("\nStopping services...")
        api_server.stop()
        core_service.stop()
        print("✓ Services stopped")
        
        return 0
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
